'use client';

import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import dynamic from 'next/dynamic';

// Dynamically import Navbar with SSR enabled
const Navbar = dynamic(() => import('@/components/Navbar'), { ssr: true });

export default function ConditionalNavbar() {
  const { data: session, status } = useSession();
  const pathname = usePathname();

  const isAuthenticated = status === 'authenticated';

  // Don't show navbar on landing page for non-authenticated users
  if (!isAuthenticated && pathname === '/') {
    return null;
  }

  // Don't show navbar on auth pages
  if (pathname?.startsWith('/login') || pathname?.startsWith('/register') || pathname?.startsWith('/auth/')) {
    return null;
  }

  // Don't show navbar on pages with dashboard sidebar (they have their own sidebar)
  if (pathname?.startsWith('/dashboard') ||
      pathname?.startsWith('/profile') ||
      pathname?.startsWith('/workflow-manager') ||
      pathname?.startsWith('/workflow-canvas') ||
      pathname?.startsWith('/workflow/') ||
      pathname?.startsWith('/workflows') ||
      pathname?.startsWith('/queue-management') ||
      pathname?.startsWith('/marketplace') ||
      pathname?.startsWith('/developer') ||
      pathname?.startsWith('/settings') ||
      pathname?.startsWith('/admin')) {
    return null;
  }

  // Show navbar for authenticated users or on other pages
  return <Navbar />;
}
