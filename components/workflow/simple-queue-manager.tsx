"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Play, Eye, Square, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';

interface SimpleQueueManagerProps {
  workflowId: string;
  workflowName: string;
  nodes: any[];
  edges: any[];
}

interface ExecutionRecord {
  id: string;
  status: string;
  startTime: string;
  endTime?: string;
  progress: number;
  error?: string;
}

interface QueueStats {
  totalQueued: number;
  totalRunning: number;
  totalCompleted: number;
  totalFailed: number;
}

export function SimpleQueueManager({ 
  workflowId, 
  workflowName, 
  nodes, 
  edges 
}: SimpleQueueManagerProps) {
  const [executions, setExecutions] = useState<ExecutionRecord[]>([]);
  const [queueStats, setQueueStats] = useState<QueueStats | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadExecutions();
  }, [workflowId]);

  const loadExecutions = async () => {
    try {
      const response = await fetch(`/api/workflows/${workflowId}/execute-background`);
      if (response.ok) {
        const data = await response.json();
        setQueueStats(data.queueStats);
        setExecutions(data.recentExecutions || []);
      }
    } catch (error) {
      console.error('Error loading executions:', error);
    }
  };

  const executeNow = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/workflows/${workflowId}/execute-background`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priority: 8,
          variables: {}
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Execution queued:', result);
        await loadExecutions();
      } else {
        const error = await response.json();
        console.error('Execution failed:', error);
      }
    } catch (error) {
      console.error('Error executing workflow:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === 'completed' ? 'default' : 
                   status === 'failed' ? 'destructive' : 
                   status === 'running' ? 'secondary' : 'outline';
    
    return (
      <Badge variant={variant} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status}
      </Badge>
    );
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = end.getTime() - start.getTime();
    
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
    return `${(duration / 60000).toFixed(1)}m`;
  };

  return (
    <div className="space-y-6">
      {/* Queue Statistics */}
      {queueStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{queueStats.totalQueued}</div>
              <div className="text-sm text-muted-foreground">Queued</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{queueStats.totalRunning}</div>
              <div className="text-sm text-muted-foreground">Running</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{queueStats.totalCompleted}</div>
              <div className="text-sm text-muted-foreground">Completed</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{queueStats.totalFailed}</div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Execution Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Background Execution</CardTitle>
          <CardDescription>
            Execute {workflowName} in the background
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button
              onClick={executeNow}
              disabled={loading || !nodes.length}
              className="flex items-center gap-2"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
              Execute Now
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.open('/queue-management', '_blank')}
            >
              Open Full Queue Manager
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Executions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Executions</CardTitle>
          <CardDescription>Latest background executions for this workflow</CardDescription>
        </CardHeader>
        <CardContent>
          {executions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No executions found. Start your first background execution above.
            </div>
          ) : (
            <ScrollArea className="h-64">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Started</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {executions.slice(0, 5).map((execution) => (
                    <TableRow key={execution.id}>
                      <TableCell>
                        {getStatusBadge(execution.status)}
                      </TableCell>
                      <TableCell>
                        {new Date(execution.startTime).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {formatDuration(execution.startTime, execution.endTime)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${execution.progress}%` }}
                            />
                          </div>
                          <span className="text-sm">{execution.progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              window.open(`/queue-management`, '_blank');
                            }}
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          {execution.status === 'queued' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                console.log('Cancel execution:', execution.id);
                              }}
                            >
                              <Square className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
