"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Play, Pause, Square, Eye, Calendar, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { ExecutionResultsViewer } from './execution-results-viewer';

interface BackgroundExecutionManagerProps {
  workflowId: string;
  workflowName: string;
  nodes: any[];
  edges: any[];
}

interface ExecutionRecord {
  id: string;
  status: string;
  startTime: string;
  endTime?: string;
  progress: number;
  error?: string;
}

interface QueueStats {
  totalQueued: number;
  totalRunning: number;
  totalCompleted: number;
  totalFailed: number;
}

export function BackgroundExecutionManager({ 
  workflowId, 
  workflowName, 
  nodes, 
  edges 
}: BackgroundExecutionManagerProps) {
  const [executions, setExecutions] = useState<ExecutionRecord[]>([]);
  const [queueStats, setQueueStats] = useState<QueueStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedExecution, setSelectedExecution] = useState<string | null>(null);
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [scheduleForm, setScheduleForm] = useState({
    priority: '5',
    scheduledAt: '',
    variables: '{}'
  });

  useEffect(() => {
    loadExecutions();
    const interval = setInterval(loadExecutions, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, [workflowId]);

  const loadExecutions = async () => {
    try {
      const response = await fetch(`/api/workflows/${workflowId}/execute-background`);
      if (response.ok) {
        const data = await response.json();
        setQueueStats(data.queueStats);
        setExecutions(data.recentExecutions || []);
      }
    } catch (error) {
      console.error('Error loading executions:', error);
    }
  };

  const executeNow = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/workflows/${workflowId}/execute-background`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priority: 8, // High priority for immediate execution
          variables: {}
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Execution queued:', result);
        await loadExecutions();
      } else {
        const error = await response.json();
        console.error('Execution failed:', error);
      }
    } catch (error) {
      console.error('Error executing workflow:', error);
    } finally {
      setLoading(false);
    }
  };

  const scheduleExecution = async () => {
    try {
      setLoading(true);
      
      let variables = {};
      try {
        variables = JSON.parse(scheduleForm.variables);
      } catch (e) {
        console.warn('Invalid JSON in variables, using empty object');
      }

      const response = await fetch(`/api/workflows/${workflowId}/execute-background`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priority: parseInt(scheduleForm.priority),
          scheduledAt: scheduleForm.scheduledAt || null,
          variables
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Execution scheduled:', result);
        setShowScheduleDialog(false);
        setScheduleForm({ priority: '5', scheduledAt: '', variables: '{}' });
        await loadExecutions();
      } else {
        const error = await response.json();
        console.error('Scheduling failed:', error);
      }
    } catch (error) {
      console.error('Error scheduling workflow:', error);
    } finally {
      setLoading(false);
    }
  };

  const cancelExecution = async (executionId: string) => {
    try {
      const response = await fetch(
        `/api/workflows/${workflowId}/execute-background?executionId=${executionId}`,
        { method: 'DELETE' }
      );

      if (response.ok) {
        await loadExecutions();
      }
    } catch (error) {
      console.error('Error cancelling execution:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === 'completed' ? 'default' : 
                   status === 'failed' ? 'destructive' : 
                   status === 'running' ? 'secondary' : 'outline';
    
    return (
      <Badge variant={variant} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status}
      </Badge>
    );
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = end.getTime() - start.getTime();
    
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
    return `${(duration / 60000).toFixed(1)}m`;
  };

  return (
    <div className="space-y-6">
      {/* Queue Statistics */}
      {queueStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{queueStats.totalQueued}</div>
              <div className="text-sm text-muted-foreground">Queued</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{queueStats.totalRunning}</div>
              <div className="text-sm text-muted-foreground">Running</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{queueStats.totalCompleted}</div>
              <div className="text-sm text-muted-foreground">Completed</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{queueStats.totalFailed}</div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Execution Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Background Execution</CardTitle>
          <CardDescription>
            Execute workflows in the background with scheduling and queue management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button
              onClick={executeNow}
              disabled={loading || !nodes.length}
              className="flex items-center gap-2"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
              Execute Now
            </Button>
            
            <Dialog open={showScheduleDialog} onOpenChange={setShowScheduleDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Schedule Execution
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Schedule Workflow Execution</DialogTitle>
                  <DialogDescription>
                    Schedule {workflowName} for background execution
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="priority">Priority (1-10)</Label>
                    <Select value={scheduleForm.priority} onValueChange={(value) => 
                      setScheduleForm(prev => ({ ...prev, priority: value }))
                    }>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[1,2,3,4,5,6,7,8,9,10].map(p => (
                          <SelectItem key={p} value={p.toString()}>{p}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="scheduledAt">Scheduled Time (optional)</Label>
                    <Input
                      id="scheduledAt"
                      type="datetime-local"
                      value={scheduleForm.scheduledAt}
                      onChange={(e) => setScheduleForm(prev => ({ ...prev, scheduledAt: e.target.value }))}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="variables">Variables (JSON)</Label>
                    <Input
                      id="variables"
                      value={scheduleForm.variables}
                      onChange={(e) => setScheduleForm(prev => ({ ...prev, variables: e.target.value }))}
                      placeholder='{"key": "value"}'
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={scheduleExecution} disabled={loading}>
                      {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                      Schedule
                    </Button>
                    <Button variant="outline" onClick={() => setShowScheduleDialog(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Execution History */}
      <Card>
        <CardHeader>
          <CardTitle>Execution History</CardTitle>
          <CardDescription>Recent background executions for this workflow</CardDescription>
        </CardHeader>
        <CardContent>
          {executions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No executions found. Start your first background execution above.
            </div>
          ) : (
            <ScrollArea className="h-96">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Started</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {executions.map((execution) => (
                    <TableRow key={execution.id}>
                      <TableCell>
                        {getStatusBadge(execution.status)}
                      </TableCell>
                      <TableCell>
                        {format(new Date(execution.startTime), 'MMM d, HH:mm:ss')}
                      </TableCell>
                      <TableCell>
                        {formatDuration(execution.startTime, execution.endTime)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${execution.progress}%` }}
                            />
                          </div>
                          <span className="text-sm">{execution.progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedExecution(execution.id)}
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          {execution.status === 'queued' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => cancelExecution(execution.id)}
                            >
                              <Square className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Results Viewer Dialog */}
      {selectedExecution && (
        <Dialog open={!!selectedExecution} onOpenChange={() => setSelectedExecution(null)}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-auto">
            <ExecutionResultsViewer 
              executionId={selectedExecution}
              onClose={() => setSelectedExecution(null)}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
