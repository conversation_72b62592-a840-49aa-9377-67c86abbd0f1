"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  Play,
  Pause,
  Square,
  Clock,
  CheckCircle,
  XCircle,
  Zap,
  Activity,
  Settings,
  Download,
  X,
  Queue,
  Calendar
} from "lucide-react";
import { Node, Edge } from "reactflow";
import {
  WorkflowExecutionEngine,
  WorkflowExecutionStatus,
  ExecutionContext,
  ExecutionOptions
} from "@/lib/workflow/execution-engine";
import { BackgroundExecutionManager } from "./background-execution-manager";

interface ExecutionPanelProps {
  workflowId: string;
  nodes: Node[];
  edges: Edge[];
  context: ExecutionContext;
  onExecutionComplete?: (status: WorkflowExecutionStatus) => void;
}

type PanelState = 'compact' | 'expanded' | 'hidden';

export function ExecutionPanel({
  workflowId,
  nodes,
  edges,
  context,
  onExecutionComplete
}: ExecutionPanelProps) {
  const [executionStatus, setExecutionStatus] = useState<WorkflowExecutionStatus | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [panelState, setPanelState] = useState<PanelState>('compact');
  const [showQueueManager, setShowQueueManager] = useState(false);
  const [executionOptions, setExecutionOptions] = useState<ExecutionOptions>({
    mode: 'optimized',
    timeout: 30000,
    retryAttempts: 3,
    continueOnError: false,
    debugMode: false,
    maxConcurrentNodes: 3
  });

  const executionEngine = WorkflowExecutionEngine.getInstance();

  // Subscribe to execution status changes
  useEffect(() => {
    if (!workflowId) return;

    const unsubscribe = executionEngine.onStatusChange(workflowId, (status) => {
      setExecutionStatus(status);
      setIsExecuting(['running', 'paused'].includes(status.status));

      if (['completed', 'failed', 'cancelled'].includes(status.status)) {
        onExecutionComplete?.(status);
      }
    });

    // Check for existing execution
    const existingStatus = executionEngine.getExecutionStatus(workflowId);
    if (existingStatus) {
      setExecutionStatus(existingStatus);
      setIsExecuting(['running', 'paused'].includes(existingStatus.status));
    }

    return unsubscribe;
  }, [workflowId, executionEngine, onExecutionComplete]);

  const handleStartExecution = useCallback(async () => {
    if (!nodes.length) {
      alert('No nodes to execute');
      return;
    }

    setIsExecuting(true);
    try {
      await executionEngine.executeWorkflow(nodes, edges, context, executionOptions);
    } catch (error) {
      console.error('Execution failed:', error);
      setIsExecuting(false);
    }
  }, [nodes, edges, context, executionOptions, executionEngine]);

  const handlePauseExecution = useCallback(async () => {
    await executionEngine.pauseWorkflow(workflowId);
  }, [workflowId, executionEngine]);

  const handleResumeExecution = useCallback(async () => {
    await executionEngine.resumeWorkflow(workflowId);
  }, [workflowId, executionEngine]);

  const handleStopExecution = useCallback(async () => {
    await executionEngine.cancelWorkflow(workflowId);
    setIsExecuting(false);
  }, [workflowId, executionEngine]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-blue-600';
      case 'paused':
        return 'text-yellow-600';
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'cancelled':
        return 'text-gray-600';
      default:
        return 'text-gray-500';
    }
  };

  const formatDuration = (start?: Date, end?: Date) => {
    if (!start) return '0s';
    const endTime = end || new Date();
    const duration = Math.floor((endTime.getTime() - start.getTime()) / 1000);

    if (duration < 60) {
      return `${duration}s`;
    } else if (duration < 3600) {
      return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    } else {
      return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`;
    }
  };

  const exportResults = () => {
    if (!executionStatus) return;

    const data = {
      workflowId: executionStatus.workflowId,
      status: executionStatus.status,
      duration: formatDuration(executionStatus.startTime, executionStatus.endTime),
      results: executionStatus.results,
      logs: executionStatus.logs
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-execution-${workflowId}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Panel state handlers

  const hidePanelTemporarily = () => {
    setPanelState('hidden');
    // Auto-show when execution starts
    if (isExecuting) {
      setTimeout(() => setPanelState('compact'), 2000);
    }
  };

  // Don't render if hidden
  if (panelState === 'hidden') {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPanelState('compact')}
                className="bg-background/98 backdrop-blur-md shadow-xl border-2 border-border/50 hover:bg-background text-foreground"
              >
                <Zap className="h-6 w-6 text-blue-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Show Execution Panel</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    );
  }

  // Get panel dimensions based on state
  const getPanelDimensions = () => {
    switch (panelState) {
      case 'compact':
        return 'w-80 h-90';
      case 'expanded':
        return 'w-80 h-98';
      default:
        return 'w-80 h-90';
    }
  };

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${getPanelDimensions()} transition-all duration-300 ease-in-out`}>
      <Card className="h-full bg-background/98 backdrop-blur-md shadow-xl border-2 border-border/50">
        {/* Header - Always visible */}
        <CardHeader className="py-2 px-4">
          <CardTitle className="flex items-center justify-between text-xs font-medium text-foreground">
            <div className="flex items-center gap-2">
              <Zap className="h-6 w-6" />
              <span>Workflow Execution</span>
              {executionStatus && (
                <Badge variant="outline" className="flex items-center gap-1 text-xs">
                  {getStatusIcon(executionStatus.status)}
                  {executionStatus.status}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={exportResults}
                      disabled={!executionStatus || executionStatus.status === 'idle'}
                      className="h-6 w-6 p-0"
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Export Results</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <Dialog open={showQueueManager} onOpenChange={setShowQueueManager}>
                <DialogTrigger asChild>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                        >
                          <Queue className="h-3 w-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Queue Management</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </DialogTrigger>
                <DialogContent className="max-w-6xl max-h-[90vh] overflow-auto">
                  <DialogHeader>
                    <DialogTitle>Background Execution Queue</DialogTitle>
                    <DialogDescription>
                      Manage background workflow executions and view queue status
                    </DialogDescription>
                  </DialogHeader>
                  <BackgroundExecutionManager
                    workflowId={workflowId}
                    workflowName={`Workflow ${workflowId}`}
                    nodes={nodes}
                    edges={edges}
                  />
                </DialogContent>
              </Dialog>



              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={hidePanelTemporarily}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Hide Panel</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </CardTitle>
        </CardHeader>

        {/* Content - Always visible */}
        <CardContent className="space-y-2 py-2 px-4 gap-2">
            {/* Execution Controls */}
            <div className="flex gap-2">
              {!isExecuting ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={handleStartExecution}
                        size="sm"
                        className="flex-1 flex items-center justify-center gap-1 h-8 text-xs"
                      >
                        <Play className="h-3 w-3" />
                        <span>Start Execution</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Start Workflow Execution</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <>
                  {executionStatus?.status === 'running' ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            onClick={handlePauseExecution}
                            variant="outline"
                            size="sm"
                            className="flex-1 h-8 text-xs"
                          >
                            <Pause className="h-3 w-3 mr-1" />
                            Pause
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Pause Execution</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            onClick={handleResumeExecution}
                            variant="outline"
                            size="sm"
                            className="flex-1 h-8 text-xs"
                          >
                            <Play className="h-3 w-3 mr-1" />
                            Resume
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Resume Execution</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={handleStopExecution}
                          variant="destructive"
                          size="sm"
                          className="flex-1 h-8 text-xs"
                        >
                          <Square className="h-3 w-3 mr-1" />
                          Stop
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Stop Execution</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </>
              )}

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 px-3"
                      onClick={() => {
                        // Toggle execution options or open settings modal
                        console.log('Execution options clicked');
                      }}
                    >
                      <Settings className="h-3 w-3" />
                      {panelState === 'expanded' && <span className="ml-1 text-xs">Options</span>}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Execution Options</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Execution Status */}
            {executionStatus && (
              <div className="space-y-1">
                {/* Progress */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span className={`text-xs ${getStatusColor(executionStatus.status)}`}>
                      {executionStatus.status === 'running' && executionStatus.currentNode
                        ? `Executing: ${executionStatus.currentNode}`
                        : executionStatus.status.charAt(0).toUpperCase() + executionStatus.status.slice(1)
                      }
                    </span>
                    <span className="text-xs font-medium">
                      {executionStatus.progress}%
                    </span>
                  </div>
                  <Progress value={executionStatus.progress} className="h-1" />
                </div>

                {/* Statistics - Responsive layout */}
                {panelState === 'compact' ? (
                  <div className="flex items-center justify-between text-xs p-1">
                    <div className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span className="text-xs">{executionStatus.completedNodes.length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <XCircle className="h-3 w-3 text-red-500" />
                      <span className="text-xs">{executionStatus.failedNodes.length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3 text-blue-500" />
                      <span className="text-xs">{formatDuration(executionStatus.startTime, executionStatus.endTime)}</span>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-4 gap-1 text-xs">
                    <div className="text-center p-1">
                      <div className="font-medium text-green-600 text-sm">
                        {executionStatus.completedNodes.length}
                      </div>
                      <div className="text-xs">Done</div>
                    </div>
                    <div className="text-center p-1">
                      <div className="font-medium text-red-600 text-sm">
                        {executionStatus.failedNodes.length}
                      </div>
                      <div className="text-xs">Failed</div>
                    </div>
                    <div className="text-center p-1">
                      <div className="font-medium text-blue-600 text-sm">
                        {nodes.length}
                      </div>
                      <div className="text-xs">Total</div>
                    </div>
                    <div className="text-center p-1">
                      <div className="font-medium text-sm">
                        {formatDuration(executionStatus.startTime, executionStatus.endTime)}
                      </div>
                      <div className="text-xs">Time</div>
                    </div>
                  </div>
                )}

                {/* Node Results - Show in both modes with different heights */}
                {Object.keys(executionStatus.results).length > 0 && (
                  <div className="space-y-1">
                    <h4 className="text-xs font-medium">Node Results</h4>
                    <ScrollArea className={panelState === 'expanded' ? 'h-64' : 'h-32'}>
                      <div className="space-y-1 pr-2">
                        {Object.entries(executionStatus.results).map(([nodeId, result]) => (
                          <div key={nodeId} className="flex items-center justify-between p-2 text-xs bg-background/50 rounded border">
                            <div className="flex items-center gap-2">
                              {result.success ? (
                                <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                              ) : (
                                <XCircle className="h-3 w-3 text-red-500 flex-shrink-0" />
                              )}
                              <span className="font-mono text-xs truncate">{nodeId}</span>
                            </div>
                            <div className="text-xs text-muted-foreground flex-shrink-0 ml-2">
                              {result.executionTime}ms
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}

                {/* Execution Logs - Only in expanded mode */}
                {panelState === 'expanded' && executionStatus.logs.length > 0 && (
                  <div className="space-y-1">
                    <h4 className="text-xs font-medium">Execution Logs</h4>
                    <ScrollArea className="h-24">
                      <div className="space-y-1 pr-2">
                        {executionStatus.logs.map((log, index) => (
                          <div key={index} className="text-xs font-mono p-2 bg-background/50 rounded border">
                            <span className="text-xs text-muted-foreground">
                              {log.timestamp.toLocaleTimeString()}
                            </span>
                            <span className={`ml-2 text-xs ${
                              log.level === 'error' ? 'text-red-600' :
                              log.level === 'warn' ? 'text-yellow-600' :
                              'text-foreground'
                            }`}>
                              [{log.level.toUpperCase()}] {log.message}
                            </span>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}
              </div>
            )}

            {/* No Execution State */}
            {!executionStatus && (
              <div className="text-center py-2">
                <Activity className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
                <p className="text-xs text-foreground">Ready to execute</p>
                {panelState === 'expanded' && (
                  <p className="text-xs text-muted-foreground">Click "Start" to begin</p>
                )}
              </div>
            )}
        </CardContent>
      </Card>
    </div>
  );
}
