"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Download, RefreshCw, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';

interface ExecutionResultsViewerProps {
  executionId: string;
  onClose?: () => void;
}

interface ExecutionSummary {
  executionId: string;
  workflowId: string;
  workflowName: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration: number;
  totalNodes: number;
  successfulNodes: number;
  failedNodes: number;
  outputData: any[];
  logs: any[];
  error?: string;
}

interface TableData {
  columns: Array<{
    key: string;
    label: string;
    type: string;
  }>;
  rows: Record<string, any>[];
  totalRows: number;
  pagination: {
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

export function ExecutionResultsViewer({ executionId, onClose }: ExecutionResultsViewerProps) {
  const [summary, setSummary] = useState<ExecutionSummary | null>(null);
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('summary');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(50);
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    loadExecutionSummary();
  }, [executionId]);

  useEffect(() => {
    if (activeTab === 'table') {
      loadTableData(currentPage);
    }
  }, [activeTab, currentPage]);

  const loadExecutionSummary = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/workflows/executions/${executionId}/results?format=summary`);
      
      if (!response.ok) {
        throw new Error('Failed to load execution results');
      }
      
      const data = await response.json();
      setSummary(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const loadTableData = async (page: number) => {
    try {
      const response = await fetch(
        `/api/workflows/executions/${executionId}/results?format=table&page=${page}&pageSize=${pageSize}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to load table data');
      }
      
      const data = await response.json();
      setTableData(data);
    } catch (err) {
      console.error('Error loading table data:', err);
    }
  };

  const exportResults = async (format: 'json' | 'csv' | 'excel') => {
    try {
      setExporting(true);
      
      const response = await fetch(`/api/workflows/executions/${executionId}/results`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format,
          includeMetadata: true,
          includeLogs: true,
          includeErrors: true
        })
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `execution-${executionId}-${Date.now()}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Export error:', err);
    } finally {
      setExporting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDuration = (duration: number) => {
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
    return `${(duration / 60000).toFixed(1)}m`;
  };

  if (loading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading execution results...</span>
        </CardContent>
      </Card>
    );
  }

  if (error || !summary) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="flex items-center justify-center py-8">
          <XCircle className="h-8 w-8 text-red-500" />
          <span className="ml-2">{error || 'Failed to load execution results'}</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(summary.status)}
              Execution Results: {summary.workflowName}
            </CardTitle>
            <CardDescription>
              Execution ID: {summary.executionId} • Started: {new Date(summary.startTime).toLocaleString()}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadExecutionSummary()}
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>
            {onClose && (
              <Button variant="outline" size="sm" onClick={onClose}>
                Close
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="table">Data Table</TabsTrigger>
            <TabsTrigger value="logs">Logs</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{summary.totalNodes}</div>
                  <div className="text-sm text-muted-foreground">Total Nodes</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">{summary.successfulNodes}</div>
                  <div className="text-sm text-muted-foreground">Successful</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-red-600">{summary.failedNodes}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{formatDuration(summary.duration)}</div>
                  <div className="text-sm text-muted-foreground">Duration</div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Execution Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant={summary.status === 'completed' ? 'default' : 'destructive'}>
                      {summary.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {summary.endTime ? `Completed at ${new Date(summary.endTime).toLocaleString()}` : 'In progress'}
                    </span>
                  </div>
                  {summary.error && (
                    <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      Error: {summary.error}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="table" className="space-y-4">
            {tableData ? (
              <Card>
                <CardHeader>
                  <CardTitle>Output Data ({tableData.totalRows} rows)</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {tableData.columns.map((column) => (
                            <TableHead key={column.key}>{column.label}</TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {tableData.rows.map((row, index) => (
                          <TableRow key={index}>
                            {tableData.columns.map((column) => (
                              <TableCell key={column.key} className="max-w-xs truncate">
                                {String(row[column.key] || '')}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                  
                  {tableData.pagination.totalPages > 1 && (
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-sm text-muted-foreground">
                        Page {tableData.pagination.page} of {tableData.pagination.totalPages}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                        >
                          Previous
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(Math.min(tableData.pagination.totalPages, currentPage + 1))}
                          disabled={currentPage === tableData.pagination.totalPages}
                        >
                          Next
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading table data...</span>
              </div>
            )}
          </TabsContent>

          <TabsContent value="logs" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Execution Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {summary.logs.map((log, index) => (
                      <div key={index} className="text-sm font-mono bg-gray-50 p-2 rounded">
                        <span className="text-gray-500">
                          {new Date(log.timestamp).toLocaleTimeString()}
                        </span>
                        <span className={`ml-2 ${log.level === 'error' ? 'text-red-600' : 'text-gray-800'}`}>
                          [{log.level.toUpperCase()}] {log.message}
                        </span>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Export Results</CardTitle>
                <CardDescription>
                  Download execution results in various formats
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={() => exportResults('json')}
                    disabled={exporting}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Export JSON
                  </Button>
                  <Button
                    onClick={() => exportResults('csv')}
                    disabled={exporting}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Export CSV
                  </Button>
                  <Button
                    onClick={() => exportResults('excel')}
                    disabled={exporting}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Export Excel
                  </Button>
                </div>
                {exporting && (
                  <div className="flex items-center justify-center mt-4">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="ml-2">Preparing export...</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
