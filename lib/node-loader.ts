// Node Loader for Dynamic Plugin System
import { NodeProps } from 'reactflow';

export interface NodeDefinition {
  id: string;
  name: string;
  type: string;
  category: string;
  description: string;
  icon: string;
  version: string;
  inputs: NodeInput[];
  outputs: NodeOutput[];
  config: NodeConfig[];
  component: React.ComponentType<NodeProps>;
  execute?: (inputs: any, config: any) => Promise<any>;
}

export interface NodeInput {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'file';
  required: boolean;
  description?: string;
  defaultValue?: any;
}

export interface NodeOutput {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'file';
  description?: string;
}

export interface NodeConfig {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea' | 'password';
  required: boolean;
  description?: string;
  defaultValue?: any;
  options?: { label: string; value: any }[];
}

export class NodeLoader {
  private static instance: NodeLoader;
  private loadedNodes: Map<string, NodeDefinition> = new Map();
  private nodeCache: Map<string, string> = new Map();

  static getInstance(): NodeLoader {
    if (!NodeLoader.instance) {
      NodeLoader.instance = new NodeLoader();
    }
    return NodeLoader.instance;
  }

  async loadNode(nodeId: string, version?: string): Promise<NodeDefinition | null> {
    try {
      // Check if node is already loaded
      const cacheKey = `${nodeId}:${version || 'latest'}`;
      if (this.loadedNodes.has(cacheKey)) {
        return this.loadedNodes.get(cacheKey)!;
      }

      // Fetch node code from API
      const response = await fetch(`/api/nodes/code/${nodeId}${version ? `?version=${version}` : ''}`);
      if (!response.ok) {
        throw new Error(`Failed to load node: ${response.statusText}`);
      }

      const { nodeCode } = await response.json();

      // Create a secure execution environment
      const nodeDefinition = await this.executeNodeCode(nodeCode);

      // Cache the loaded node
      this.loadedNodes.set(cacheKey, nodeDefinition);

      return nodeDefinition;
    } catch (error) {
      console.error(`Failed to load node ${nodeId}:`, error);
      return null;
    }
  }

  async executeNodeCode(nodeCode: any): Promise<NodeDefinition> {
    return new Promise((resolve, reject) => {
      try {
        // Validate nodeCode structure
        if (!nodeCode || typeof nodeCode.code !== 'string') {
          reject(new Error('Invalid node code: missing or invalid code property'));
          return;
        }

        // Check if we're in a browser environment that supports Web Workers
        if (typeof Worker === 'undefined') {
          // Fallback for server-side or environments without Web Worker support
          try {
            // Simple eval-based execution (less secure but functional)
            const nodeDefinition = this.executeNodeCodeDirectly(nodeCode.code);
            resolve(nodeDefinition);
            return;
          } catch (error) {
            reject(new Error(`Direct execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`));
            return;
          }
        }

        // Create a Web Worker for secure execution
        const workerCode = `
          try {
            // Execute the node code
            ${nodeCode.code}

            // The node code should define a nodeDefinition variable
            // Send it back to the main thread
            if (typeof nodeDefinition !== 'undefined') {
              // Serialize the node definition to avoid cloning issues with functions
              const serializedDefinition = {
                id: nodeDefinition.id,
                name: nodeDefinition.name,
                description: nodeDefinition.description,
                category: nodeDefinition.category,
                version: nodeDefinition.version,
                icon: nodeDefinition.icon,
                inputs: nodeDefinition.inputs,
                outputs: nodeDefinition.outputs,
                config: nodeDefinition.config,
                // Convert execute function to string
                executeCode: nodeDefinition.execute ? nodeDefinition.execute.toString() : null
              };
              self.postMessage({ type: 'definition', data: serializedDefinition });
            } else {
              self.postMessage({ type: 'error', error: 'nodeDefinition not found in node code' });
            }
          } catch (error) {
            self.postMessage({
              type: 'error',
              error: 'Failed to execute node code: ' + (error.message || error.toString())
            });
          }
        `;

        const blob = new Blob([workerCode], { type: 'application/javascript' });
        const blobUrl = URL.createObjectURL(blob);
        const worker = new Worker(blobUrl);

        const timeout = setTimeout(() => {
          worker.terminate();
          URL.revokeObjectURL(blobUrl);
          reject(new Error('Node loading timeout (5s)'));
        }, 5000);

        worker.onmessage = (event) => {
          clearTimeout(timeout);
          worker.terminate();
          URL.revokeObjectURL(blobUrl);

          if (event.data.type === 'definition') {
            // Reconstruct the node definition from serialized data
            const serializedDefinition = event.data.data;

            if (!serializedDefinition.id || !serializedDefinition.name || !serializedDefinition.executeCode) {
              reject(new Error('Invalid node definition: missing required fields (id, name, executeCode)'));
              return;
            }

            // Reconstruct the execute function from the serialized code
            let executeFunction;
            try {
              // Handle both function declarations and arrow functions
              const functionCode = serializedDefinition.executeCode;
              if (functionCode.startsWith('function') || functionCode.startsWith('async function')) {
                executeFunction = new Function('return ' + functionCode)();
              } else if (functionCode.includes('=>')) {
                // Arrow function
                executeFunction = new Function('return ' + functionCode)();
              } else {
                // Fallback: try to wrap it as a function
                executeFunction = new Function('inputs', 'config', functionCode);
              }
            } catch (error) {
              console.warn('[NodeLoader] Failed to reconstruct execute function, creating fallback:', error);
              // Create a fallback function that returns the inputs
              executeFunction = (inputs: any, config: any) => {
                console.log(`[NodeLoader] Fallback execution for node ${serializedDefinition.id}`);
                return inputs;
              };
            }

            const definition: NodeDefinition = {
              id: serializedDefinition.id,
              name: serializedDefinition.name,
              description: serializedDefinition.description,
              category: serializedDefinition.category,
              version: serializedDefinition.version,
              icon: serializedDefinition.icon,
              inputs: serializedDefinition.inputs,
              outputs: serializedDefinition.outputs,
              config: serializedDefinition.config,
              execute: executeFunction
            };

            resolve(definition);
          } else {
            reject(new Error(event.data.error || 'Unknown error loading node'));
          }
        };

        worker.onerror = (error) => {
          clearTimeout(timeout);
          worker.terminate();
          URL.revokeObjectURL(blobUrl);
          reject(new Error(`Worker error: ${error.message || 'Unknown worker error'}`));
        };

      } catch (error) {
        reject(new Error(`Failed to create worker: ${error instanceof Error ? error.message : 'Unknown error'}`));
      }
    });
  }

  private executeNodeCodeDirectly(code: string): NodeDefinition {
    // Create a safe execution context
    const context = {
      nodeDefinition: null as any,
      console: {
        log: (...args: any[]) => console.log('[NodeLoader]', ...args),
        error: (...args: any[]) => console.error('[NodeLoader]', ...args),
        warn: (...args: any[]) => console.warn('[NodeLoader]', ...args)
      }
    };

    // Create a function that executes the code in the context
    const executeCode = new Function('context', `
      with (context) {
        ${code}
        return nodeDefinition;
      }
    `);

    const result = executeCode(context);

    if (!result || !result.id || !result.name || !result.execute) {
      throw new Error('Invalid node definition: missing required fields (id, name, execute)');
    }

    // The result already has the execute function, so we can return it directly
    // No need for serialization/deserialization in direct execution
    return result;
  }

  async getInstalledNodes(): Promise<NodeDefinition[]> {
    try {
      const response = await fetch('/api/nodes/installed');
      if (!response.ok) {
        throw new Error('Failed to fetch installed nodes');
      }

      const { installedNodes } = await response.json();
      const nodeDefinitions: NodeDefinition[] = [];

      for (const installation of installedNodes) {
        if (installation.enabled && installation.status === 'installed') {
          const nodeDefinition = await this.loadNode(installation.nodeId, installation.version);
          if (nodeDefinition) {
            nodeDefinitions.push(nodeDefinition);
          }
        }
      }

      return nodeDefinitions;
    } catch (error) {
      console.error('Failed to get installed nodes:', error);
      return [];
    }
  }

  async installNode(nodeId: string, version?: string): Promise<boolean> {
    try {
      const response = await fetch('/api/nodes/install', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId, version }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Installation failed');
      }

      // Clear cache to force reload
      const cacheKey = `${nodeId}:${version || 'latest'}`;
      this.loadedNodes.delete(cacheKey);

      return true;
    } catch (error) {
      console.error(`Failed to install node ${nodeId}:`, error);
      return false;
    }
  }

  async uninstallNode(nodeId: string): Promise<boolean> {
    try {
      const response = await fetch('/api/nodes/uninstall', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Uninstallation failed');
      }

      // Remove from cache
      for (const [key] of this.loadedNodes) {
        if (key.startsWith(`${nodeId}:`)) {
          this.loadedNodes.delete(key);
        }
      }

      return true;
    } catch (error) {
      console.error(`Failed to uninstall node ${nodeId}:`, error);
      return false;
    }
  }

  getLoadedNode(nodeId: string, version?: string): NodeDefinition | null {
    const cacheKey = `${nodeId}:${version || 'latest'}`;
    return this.loadedNodes.get(cacheKey) || null;
  }

  clearCache(): void {
    this.loadedNodes.clear();
    this.nodeCache.clear();
  }
}

// Export singleton instance
export const nodeLoader = NodeLoader.getInstance();
