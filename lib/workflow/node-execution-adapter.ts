/**
 * Node Execution Adapter
 * Bridges UI nodes with the execution engine
 */

import { Node } from 'reactflow';
import { nodeRegistry } from './node-registry';
import { NodeExecutionResult, ExecutionContext } from './execution-engine';
import { NodeExecutionContext, NodeExecutionOutput, ExecutionHelpers } from './execution-interface';

/**
 * Adapter class that enables UI nodes to be executed by the workflow engine
 */
export class NodeExecutionAdapter {
  /**
   * Execute a UI node within the workflow execution context
   */
  async executeUINode(
    node: Node,
    inputs: Record<string, any>,
    context: ExecutionContext
  ): Promise<NodeExecutionResult> {
    const startTime = Date.now();

    try {
      // Check if node has a type
      if (!node.type) {
        throw new Error(`Node ${node.id} has no type defined`);
      }

      // Get node registration
      const registration = nodeRegistry.getNodeRegistration(node.type);

      if (!registration) {
        throw new Error(`Node type '${node.type}' not found in registry`);
      }

      if (!registration.execution) {
        throw new Error(`Node type '${node.type}' does not have execution capabilities`);
      }

      // Extract configuration from node data
      const config = this.extractNodeConfig(node.data);

      // Create node execution context
      const nodeContext: NodeExecutionContext = {
        ...context,
        nodeId: node.id,
        nodeType: node.type,
        nodeData: node.data,
        log: (message: string, level: string) => {
          console.log(`[${node.type}:${node.id}] ${level.toUpperCase()}: ${message}`);
        },
        reportProgress: (progress: number) => {
          console.log(`[${node.type}:${node.id}] Progress: ${progress}%`);
        },
        isCancelled: () => false // TODO: Implement cancellation
      };

      // Execute the node
      const outputs = await registration.execution.execute(inputs, config, nodeContext);

      const executionTime = Date.now() - startTime;

      return {
        nodeId: node.id,
        success: true,
        outputs,
        executionTime,
        logs: [`Node executed successfully in ${executionTime}ms`],
        metadata: {
          nodeType: node.type,
          nodeLabel: registration.metadata.label
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      return {
        nodeId: node.id,
        success: false,
        outputs: {},
        error: errorMessage,
        executionTime,
        logs: [`Execution failed: ${errorMessage}`],
        metadata: {
          nodeType: node.type || 'unknown',
          errorType: error instanceof Error ? error.constructor.name : 'Unknown'
        }
      };
    }
  }

  /**
   * Extract configuration from node data
   */
  private extractNodeConfig(nodeData: any): Record<string, any> {
    if (!nodeData) return {};

    // Extract common configuration properties
    const config: Record<string, any> = {};

    // Standard properties
    if (nodeData.value !== undefined) config.value = nodeData.value;
    if (nodeData.inputValue !== undefined) config.inputValue = nodeData.inputValue;
    if (nodeData.fileContent !== undefined) config.fileContent = nodeData.fileContent;
    if (nodeData.label !== undefined) config.label = nodeData.label;

    // Custom properties (copy all other properties)
    Object.keys(nodeData).forEach(key => {
      if (!['onChange', 'onRawDataChange', 'onJsonChange'].includes(key)) {
        config[key] = nodeData[key];
      }
    });

    return config;
  }

  /**
   * Check if a node type can be executed
   */
  canExecuteNode(nodeType: string): boolean {
    return nodeRegistry.hasExecution(nodeType);
  }

  /**
   * Get execution metadata for a node type
   */
  getExecutionMetadata(nodeType: string) {
    const registration = nodeRegistry.getNodeRegistration(nodeType);
    return registration?.execution;
  }

  /**
   * Validate node inputs against execution schema
   */
  validateNodeInputs(nodeType: string, inputs: Record<string, any>): { valid: boolean; errors: string[] } {
    const execution = this.getExecutionMetadata(nodeType);

    if (!execution) {
      return { valid: false, errors: [`Node type '${nodeType}' has no execution definition`] };
    }

    return ExecutionHelpers.validateInputs(inputs, execution.inputs);
  }

  /**
   * Get input schema for a node type
   */
  getNodeInputSchema(nodeType: string) {
    const execution = this.getExecutionMetadata(nodeType);
    return execution?.inputs || [];
  }

  /**
   * Get output schema for a node type
   */
  getNodeOutputSchema(nodeType: string) {
    const execution = this.getExecutionMetadata(nodeType);
    return execution?.outputs || [];
  }

  /**
   * Create a mock execution result for testing
   */
  createMockExecutionResult(
    nodeId: string,
    nodeType: string,
    success: boolean = true
  ): NodeExecutionResult {
    const execution = this.getExecutionMetadata(nodeType);
    const outputs: Record<string, any> = {};

    if (execution && success) {
      // Create mock outputs based on schema
      execution.outputs.forEach(output => {
        switch (output.type) {
          case 'string':
            outputs[output.id] = `Mock ${output.name}`;
            break;
          case 'number':
            outputs[output.id] = 42;
            break;
          case 'boolean':
            outputs[output.id] = true;
            break;
          case 'object':
            outputs[output.id] = { mock: true };
            break;
          case 'array':
            outputs[output.id] = ['mock', 'data'];
            break;
          default:
            outputs[output.id] = `Mock ${output.type}`;
        }
      });
    }

    return {
      nodeId,
      success,
      outputs,
      error: success ? undefined : 'Mock execution error',
      executionTime: Math.random() * 1000,
      logs: success ? ['Mock execution completed'] : ['Mock execution failed'],
      metadata: {
        nodeType,
        mock: true
      }
    };
  }

  /**
   * Get all executable node types
   */
  getExecutableNodeTypes(): string[] {
    return nodeRegistry.getAllNodes()
      .filter(node => nodeRegistry.hasExecution(node.type))
      .map(node => node.type);
  }

  /**
   * Get execution statistics for all nodes
   */
  getExecutionStats() {
    const allNodes = nodeRegistry.getAllNodes();
    const executableNodes = allNodes.filter(node => nodeRegistry.hasExecution(node.type));

    return {
      totalNodes: allNodes.length,
      executableNodes: executableNodes.length,
      executionCoverage: allNodes.length > 0 ? (executableNodes.length / allNodes.length) * 100 : 0,
      nodeTypes: {
        executable: executableNodes.map(node => ({
          type: node.type,
          label: node.label,
          category: node.category
        })),
        nonExecutable: allNodes
          .filter(node => !nodeRegistry.hasExecution(node.type))
          .map(node => ({
            type: node.type,
            label: node.label,
            category: node.category
          }))
      }
    };
  }
}

// Export singleton instance
export const nodeExecutionAdapter = new NodeExecutionAdapter();
