/**
 * Advanced Workflow Execution Engine with Web Workers
 */

import { Node, Edge } from 'reactflow';
import { NodeDefinition } from '@/lib/node-loader';
import { nodeRegistry } from './node-registry';
import { nodeExecutionAdapter } from './node-execution-adapter';

export interface ExecutionContext {
  log(arg0: string, arg1: string): unknown;
  workflowId: string;
  userId: string;
  variables: Record<string, any>;
  secrets: Record<string, string>;
  settings: Record<string, any>;
}

export interface NodeExecutionResult {
  nodeId: string;
  success: boolean;
  outputs: Record<string, any>;
  error?: string;
  executionTime: number;
  logs: string[];
  metadata?: Record<string, any>;
}

export interface WorkflowExecutionStatus {
  workflowId: string;
  status: 'idle' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled' | 'queued';
  startTime?: Date;
  endTime?: Date;
  currentNode?: string;
  completedNodes: string[];
  failedNodes: string[];
  progress: number; // 0-100
  results: Record<string, NodeExecutionResult>;
  logs: Array<{
    timestamp: Date;
    level: 'info' | 'warn' | 'error' | 'debug';
    message: string;
    nodeId?: string;
  }>;
  executionId?: string; // Database execution record ID
  isBackground?: boolean; // Whether this is a background execution
  queuePosition?: number; // Position in execution queue
}

export interface ExecutionOptions {
  mode: 'sequential' | 'parallel' | 'optimized';
  timeout?: number; // milliseconds
  retryAttempts?: number;
  continueOnError?: boolean;
  debugMode?: boolean;
  maxConcurrentNodes?: number;
  backgroundExecution?: boolean; // Enable background processing
  saveResults?: boolean; // Save results to database
  notifyOnComplete?: boolean; // Send notifications when complete
}

export class WorkflowExecutionEngine {
  private static instance: WorkflowExecutionEngine;
  private activeExecutions = new Map<string, WorkflowExecutionStatus>();
  private workers = new Map<string, Worker>();
  private executionCallbacks = new Map<string, ((status: WorkflowExecutionStatus) => void)[]>();

  static getInstance(): WorkflowExecutionEngine {
    if (!WorkflowExecutionEngine.instance) {
      WorkflowExecutionEngine.instance = new WorkflowExecutionEngine();
    }
    return WorkflowExecutionEngine.instance;
  }

  /**
   * Execute a complete workflow
   */
  async executeWorkflow(
    nodes: Node[],
    edges: Edge[],
    context: ExecutionContext,
    options: ExecutionOptions = { mode: 'optimized' }
  ): Promise<WorkflowExecutionStatus> {
    const workflowId = context.workflowId;

    // Initialize execution status
    const status: WorkflowExecutionStatus = {
      workflowId,
      status: 'running',
      startTime: new Date(),
      completedNodes: [],
      failedNodes: [],
      progress: 0,
      results: {},
      logs: []
    };

    this.activeExecutions.set(workflowId, status);
    this.notifyStatusChange(workflowId, status);

    try {
      // Validate workflow
      const validation = this.validateWorkflow(nodes, edges);
      if (!validation.valid) {
        throw new Error(`Workflow validation failed: ${validation.errors.join(', ')}`);
      }

      // Build execution graph
      const executionGraph = this.buildExecutionGraph(nodes, edges);

      // Execute based on mode
      let results: Record<string, NodeExecutionResult>;

      switch (options.mode) {
        case 'sequential':
          results = await this.executeSequential(executionGraph, context, options);
          break;
        case 'parallel':
          results = await this.executeParallel(executionGraph, context, options);
          break;
        case 'optimized':
        default:
          results = await this.executeOptimized(executionGraph, context, options);
          break;
      }

      // Update final status
      const finalStatus: WorkflowExecutionStatus = {
        ...status,
        status: 'completed',
        endTime: new Date(),
        progress: 100,
        results,
        completedNodes: Object.keys(results).filter(id => results[id].success),
        failedNodes: Object.keys(results).filter(id => !results[id].success)
      };

      this.activeExecutions.set(workflowId, finalStatus);
      this.notifyStatusChange(workflowId, finalStatus);

      return finalStatus;

    } catch (error) {
      const errorStatus: WorkflowExecutionStatus = {
        ...status,
        status: 'failed',
        endTime: new Date(),
        logs: [...status.logs, {
          timestamp: new Date(),
          level: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        }]
      };

      this.activeExecutions.set(workflowId, errorStatus);
      this.notifyStatusChange(workflowId, errorStatus);

      return errorStatus;
    }
  }

  /**
   * Execute a single node
   */
  async executeNode(
    node: Node,
    inputs: Record<string, any>,
    context: ExecutionContext,
    options: ExecutionOptions = { mode: 'optimized' }
  ): Promise<NodeExecutionResult> {
    try {
      console.log(`[ExecutionEngine] Executing node ${node.id} of type ${node.type}`);

      // Check if this is a UI node with execution capabilities
      if (node.type && nodeExecutionAdapter.canExecuteNode(node.type)) {
        console.log(`[ExecutionEngine] Using adapter for UI node ${node.id}`);
        // Use the adapter for UI nodes
        return await nodeExecutionAdapter.executeUINode(node, inputs, context);
      }

      // Fallback to traditional node loader for marketplace nodes
      const startTime = Date.now();
      const logs: string[] = [];

      // Get node definition
      if (!node.type) {
        throw new Error(`Node ${node.id} has no type defined`);
      }
      const nodeDefinition = await this.getNodeDefinition(node.type);
      if (!nodeDefinition) {
        throw new Error(`Node definition not found for type: ${node.type}`);
      }

      // Prepare execution environment
      const executionEnv = {
        nodeId: node.id,
        nodeType: node.type,
        nodeData: node.data,
        inputs,
        context,
        options,
        log: (message: string) => logs.push(`[${new Date().toISOString()}] ${message}`)
      };

      // Check if we're in a server environment (no Web Workers available)
      const isServerSide = typeof window === 'undefined';

      let outputs: Record<string, any>;

      if (isServerSide) {
        console.log(`[ExecutionEngine] Executing node ${node.id} in server-side mode`);
        // Execute directly in server environment without Web Workers
        outputs = await this.executeDirectly(nodeDefinition, executionEnv);
      } else {
        console.log(`[ExecutionEngine] Executing node ${node.id} in Web Worker`);
        // Execute in Web Worker for isolation (browser only)
        outputs = await this.executeInWorker(nodeDefinition, executionEnv, options.timeout);
      }

      console.log(`[ExecutionEngine] Node ${node.id} executed successfully`);

      return {
        nodeId: node.id,
        success: true,
        outputs,
        executionTime: Date.now() - startTime,
        logs
      };

    } catch (error) {
      console.error(`[ExecutionEngine] Node ${node.id} execution failed:`, error);
      return {
        nodeId: node.id,
        success: false,
        outputs: {},
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: 0,
        logs: [`Execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }

  /**
   * Pause workflow execution
   */
  async pauseWorkflow(workflowId: string): Promise<void> {
    const status = this.activeExecutions.get(workflowId);
    if (status && status.status === 'running') {
      status.status = 'paused';
      this.activeExecutions.set(workflowId, status);
      this.notifyStatusChange(workflowId, status);
    }
  }

  /**
   * Resume workflow execution
   */
  async resumeWorkflow(workflowId: string): Promise<void> {
    const status = this.activeExecutions.get(workflowId);
    if (status && status.status === 'paused') {
      status.status = 'running';
      this.activeExecutions.set(workflowId, status);
      this.notifyStatusChange(workflowId, status);
    }
  }

  /**
   * Cancel workflow execution
   */
  async cancelWorkflow(workflowId: string): Promise<void> {
    const status = this.activeExecutions.get(workflowId);
    if (status && ['running', 'paused'].includes(status.status)) {
      // Terminate any active workers
      const worker = this.workers.get(workflowId);
      if (worker) {
        worker.terminate();
        this.workers.delete(workflowId);
      }

      status.status = 'cancelled';
      status.endTime = new Date();
      this.activeExecutions.set(workflowId, status);
      this.notifyStatusChange(workflowId, status);
    }
  }

  /**
   * Get execution status
   */
  getExecutionStatus(workflowId: string): WorkflowExecutionStatus | null {
    return this.activeExecutions.get(workflowId) || null;
  }

  /**
   * Subscribe to execution status changes
   */
  onStatusChange(workflowId: string, callback: (status: WorkflowExecutionStatus) => void): () => void {
    if (!this.executionCallbacks.has(workflowId)) {
      this.executionCallbacks.set(workflowId, []);
    }

    this.executionCallbacks.get(workflowId)!.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.executionCallbacks.get(workflowId);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  private validateWorkflow(nodes: Node[], edges: Edge[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for cycles
    if (this.hasCycles(nodes, edges)) {
      errors.push('Workflow contains cycles');
    }

    // Check for disconnected nodes
    const disconnectedNodes = this.findDisconnectedNodes(nodes, edges);
    if (disconnectedNodes.length > 0) {
      errors.push(`Disconnected nodes found: ${disconnectedNodes.join(', ')}`);
    }

    // Validate node types
    for (const node of nodes) {
      if (!node.type) {
        errors.push(`Node ${node.id} has no type defined`);
      } else if (!nodeRegistry.hasNode(node.type)) {
        errors.push(`Unknown node type: ${node.type}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private buildExecutionGraph(nodes: Node[], edges: Edge[]): Map<string, { node: Node; dependencies: string[]; dependents: string[] }> {
    const graph = new Map();

    // Initialize nodes
    for (const node of nodes) {
      graph.set(node.id, {
        node,
        dependencies: [],
        dependents: []
      });
    }

    // Build dependencies
    for (const edge of edges) {
      const source = graph.get(edge.source);
      const target = graph.get(edge.target);

      if (source && target) {
        target.dependencies.push(edge.source);
        source.dependents.push(edge.target);
      }
    }

    return graph;
  }

  private async executeOptimized(
    graph: Map<string, any>,
    context: ExecutionContext,
    options: ExecutionOptions
  ): Promise<Record<string, NodeExecutionResult>> {
    const results: Record<string, NodeExecutionResult> = {};
    const completed = new Set<string>();
    const executing = new Set<string>();
    const maxConcurrent = options.maxConcurrentNodes || 3;

    while (completed.size < graph.size) {
      // Find nodes ready to execute
      const readyNodes = Array.from(graph.entries())
        .filter(([nodeId, nodeInfo]) =>
          !completed.has(nodeId) &&
          !executing.has(nodeId) &&
          nodeInfo.dependencies.every((dep: string) => completed.has(dep))
        )
        .slice(0, maxConcurrent - executing.size);

      if (readyNodes.length === 0 && executing.size === 0) {
        throw new Error('Workflow execution deadlock detected');
      }

      // Execute ready nodes in parallel
      const executions = readyNodes.map(async ([nodeId, nodeInfo]) => {
        executing.add(nodeId);

        try {
          // Collect inputs from dependencies
          const inputs: Record<string, any> = {};
          for (const depId of nodeInfo.dependencies) {
            const depResult = results[depId];
            if (depResult && depResult.success) {
              Object.assign(inputs, depResult.outputs);
            }
          }

          const result = await this.executeNode(nodeInfo.node, inputs, context, options);
          results[nodeId] = result;
          completed.add(nodeId);

        } catch (error) {
          results[nodeId] = {
            nodeId,
            success: false,
            outputs: {},
            error: error instanceof Error ? error.message : 'Unknown error',
            executionTime: 0,
            logs: []
          };
          completed.add(nodeId);
        } finally {
          executing.delete(nodeId);
        }
      });

      await Promise.all(executions);
    }

    return results;
  }

  private async executeSequential(
    graph: Map<string, any>,
    context: ExecutionContext,
    options: ExecutionOptions
  ): Promise<Record<string, NodeExecutionResult>> {
    // Topological sort for sequential execution
    const sorted = this.topologicalSort(graph);
    const results: Record<string, NodeExecutionResult> = {};

    console.log(`[ExecutionEngine] Sequential execution order: ${sorted.join(' -> ')}`);

    for (let i = 0; i < sorted.length; i++) {
      const nodeId = sorted[i];
      const nodeInfo = graph.get(nodeId);
      if (!nodeInfo) continue;

      console.log(`[ExecutionEngine] Executing node ${i + 1}/${sorted.length}: ${nodeId}`);

      // Update current node in status
      const status = this.activeExecutions.get(context.workflowId);
      if (status) {
        status.currentNode = nodeId;
        status.progress = Math.round((i / sorted.length) * 100);
        this.notifyStatusChange(context.workflowId, status);
      }

      // Collect inputs from dependencies
      const inputs: Record<string, any> = {};
      for (const depId of nodeInfo.dependencies) {
        const depResult = results[depId];
        if (depResult && depResult.success) {
          // Merge outputs from dependency nodes
          Object.assign(inputs, depResult.outputs);
          console.log(`[ExecutionEngine] Node ${nodeId} received input from ${depId}:`, Object.keys(depResult.outputs));
        } else if (depResult && !depResult.success) {
          console.warn(`[ExecutionEngine] Dependency ${depId} failed, but continuing execution`);
        }
      }

      try {
        const result = await this.executeNode(nodeInfo.node, inputs, context, options);
        results[nodeId] = result;

        // Update status
        if (status) {
          if (result.success) {
            status.completedNodes.push(nodeId);
          } else {
            status.failedNodes.push(nodeId);
          }
          this.notifyStatusChange(context.workflowId, status);
        }

        console.log(`[ExecutionEngine] Node ${nodeId} execution result:`, {
          success: result.success,
          outputKeys: Object.keys(result.outputs),
          executionTime: result.executionTime
        });

        // Stop execution if node failed and continueOnError is false
        if (!result.success && !options.continueOnError) {
          console.log(`[ExecutionEngine] Stopping execution due to node failure: ${nodeId}`);
          break;
        }
      } catch (error) {
        console.error(`[ExecutionEngine] Error executing node ${nodeId}:`, error);

        const errorResult: NodeExecutionResult = {
          nodeId,
          success: false,
          outputs: {},
          error: error instanceof Error ? error.message : 'Unknown error',
          executionTime: 0,
          logs: [`Error: ${error instanceof Error ? error.message : 'Unknown error'}`]
        };

        results[nodeId] = errorResult;

        if (status) {
          status.failedNodes.push(nodeId);
          this.notifyStatusChange(context.workflowId, status);
        }

        if (!options.continueOnError) {
          break;
        }
      }
    }

    return results;
  }

  private async executeParallel(
    graph: Map<string, any>,
    context: ExecutionContext,
    options: ExecutionOptions
  ): Promise<Record<string, NodeExecutionResult>> {
    const executions = Array.from(graph.entries()).map(async ([nodeId, nodeInfo]) => {
      const result = await this.executeNode(nodeInfo.node, {}, context, options);
      return [nodeId, result] as [string, NodeExecutionResult];
    });

    const results = await Promise.all(executions);
    return Object.fromEntries(results);
  }

  /**
   * Execute node directly in server environment (no Web Workers)
   */
  private async executeDirectly(
    nodeDefinition: NodeDefinition,
    executionEnv: any
  ): Promise<Record<string, any>> {
    try {
      console.log(`[ExecutionEngine] Executing node directly: ${executionEnv.nodeId}`);

      // Create a safe execution context
      const safeContext = {
        ...executionEnv,
        // Add any additional server-side specific context
        isServerSide: true
      };

      // For server-side execution, we need to evaluate the node code safely
      // This is a simplified approach - in production, you might want to use a more secure sandbox

      // Create a function from the node definition code
      const nodeFunction = new Function('executionEnv', `
        ${(nodeDefinition as any).code}

        // Call the executeNode function that should be defined in the node code
        if (typeof executeNode === 'function') {
          return executeNode(executionEnv);
        } else {
          throw new Error('executeNode function not found in node definition');
        }
      `);

      // Execute the node function
      const result = await nodeFunction(safeContext);

      console.log(`[ExecutionEngine] Node ${executionEnv.nodeId} executed directly with result:`, result);

      return result || {};

    } catch (error) {
      console.error(`[ExecutionEngine] Direct execution failed for node ${executionEnv.nodeId}:`, error);
      throw error;
    }
  }

  private async executeInWorker(
    nodeDefinition: NodeDefinition,
    executionEnv: any,
    timeout: number = 30000
  ): Promise<Record<string, any>> {
    return new Promise((resolve, reject) => {
      const workerCode = `
        try {
          // Execute the node code to get the definition
          ${(nodeDefinition as any).code}

          self.onmessage = function(e) {
            const { executionEnv } = e.data;

            try {
              // Check if nodeDefinition is available and has execute function
              if (typeof nodeDefinition !== 'undefined' && typeof nodeDefinition.execute === 'function') {
                // Execute the node using the definition's execute function
                const result = nodeDefinition.execute(executionEnv.inputs, executionEnv.config);

                // Handle both sync and async results
                Promise.resolve(result).then(finalResult => {
                  self.postMessage({ type: 'success', result: finalResult });
                }).catch(error => {
                  self.postMessage({
                    type: 'error',
                    error: error.message || 'Execution failed'
                  });
                });
              } else {
                self.postMessage({
                  type: 'error',
                  error: 'nodeDefinition or execute function not found'
                });
              }
            } catch (error) {
              self.postMessage({
                type: 'error',
                error: error.message || 'Execution failed'
              });
            }
          };
        } catch (error) {
          self.postMessage({
            type: 'error',
            error: 'Failed to initialize node: ' + (error.message || error.toString())
          });
        }
      `;

      const blob = new Blob([workerCode], { type: 'application/javascript' });
      const blobUrl = URL.createObjectURL(blob);
      const worker = new Worker(blobUrl);

      const timeoutId = setTimeout(() => {
        worker.terminate();
        URL.revokeObjectURL(blobUrl);
        reject(new Error('Node execution timeout'));
      }, timeout);

      worker.onmessage = (e) => {
        clearTimeout(timeoutId);
        worker.terminate();
        URL.revokeObjectURL(blobUrl);

        if (e.data.type === 'success') {
          resolve(e.data.result || {});
        } else {
          reject(new Error(e.data.error || 'Unknown execution error'));
        }
      };

      worker.onerror = (error) => {
        clearTimeout(timeoutId);
        worker.terminate();
        URL.revokeObjectURL(blobUrl);
        reject(new Error(`Worker error: ${error.message || 'Unknown worker error'}`));
      };

      worker.postMessage({ executionEnv });
    });
  }

  private async getNodeDefinition(nodeType: string): Promise<NodeDefinition | null> {
    // Try to get from registry first (built-in nodes)
    const metadata = nodeRegistry.getNodeMetadata(nodeType);
    if (metadata) {
      // For built-in nodes, we need to create a definition
      return {
        id: nodeType,
        type: nodeType,
        name: metadata.label,
        description: metadata.description,
        category: metadata.category,
        version: '1.0.0',
        code: `
          function executeNode(env) {
            // Built-in node execution logic
            return env.inputs;
          }
        `,
        icon: metadata.icon
      } as any;
    }

    // For installed nodes, try to load from the node code API
    if (nodeType === 'installed-node') {
      // This is handled by the node execution adapter, so return a basic definition
      return {
        id: 'installed-node',
        type: 'installed-node',
        name: 'Installed Node',
        description: 'Node from marketplace',
        category: 'advanced',
        version: '1.0.0',
        execute: async (inputs: any) => {
          // Basic fallback execution
          return {
            output: `Processed: ${inputs.input || 'No input'} (Fallback execution)`
          };
        }
      } as any;
    }

    // Try to load from installed marketplace nodes by node ID
    try {
      const response = await fetch(`/api/nodes/code/${nodeType}`);
      if (response.ok) {
        const codeData = await response.json();
        return codeData.nodeDefinition || null;
      }
    } catch (error) {
      console.error('Failed to load node definition:', error);
    }

    return null;
  }

  private hasCycles(nodes: Node[], edges: Edge[]): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const outgoingEdges = edges.filter(edge => edge.source === nodeId);
      for (const edge of outgoingEdges) {
        if (dfs(edge.target)) return true;
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of nodes) {
      if (!visited.has(node.id) && dfs(node.id)) {
        return true;
      }
    }

    return false;
  }

  private findDisconnectedNodes(nodes: Node[], edges: Edge[]): string[] {
    const connectedNodes = new Set<string>();

    for (const edge of edges) {
      connectedNodes.add(edge.source);
      connectedNodes.add(edge.target);
    }

    return nodes
      .filter(node => !connectedNodes.has(node.id))
      .map(node => node.id);
  }

  private topologicalSort(graph: Map<string, any>): string[] {
    const visited = new Set<string>();
    const result: string[] = [];

    const dfs = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);

      const nodeInfo = graph.get(nodeId);
      if (nodeInfo) {
        for (const depId of nodeInfo.dependencies) {
          dfs(depId);
        }
      }

      result.push(nodeId);
    };

    for (const nodeId of graph.keys()) {
      dfs(nodeId);
    }

    return result;
  }

  private notifyStatusChange(workflowId: string, status: WorkflowExecutionStatus): void {
    const callbacks = this.executionCallbacks.get(workflowId) || [];
    callbacks.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('Error in execution status callback:', error);
      }
    });
  }
}
