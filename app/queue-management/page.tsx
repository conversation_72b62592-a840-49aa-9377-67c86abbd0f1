"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, Eye, Square, CheckCircle, XCircle, Clock, AlertCircle, Play } from 'lucide-react';

interface QueueStats {
  totalQueued: number;
  totalRunning: number;
  totalCompleted: number;
  totalFailed: number;
}

interface ExecutionRecord {
  id: string;
  workflowId: string;
  workflowName?: string;
  status: string;
  startTime: string;
  endTime?: string;
  progress: number;
  error?: string;
  userId: string;
}

export default function QueueManagementPage() {
  const [queueStats, setQueueStats] = useState<QueueStats | null>(null);
  const [executions, setExecutions] = useState<ExecutionRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    loadQueueData();
    const interval = setInterval(loadQueueData, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const loadQueueData = async () => {
    try {
      setLoading(true);
      
      // Load queue statistics and recent executions
      // Since we don't have a global queue API, we'll simulate it
      const mockStats: QueueStats = {
        totalQueued: 2,
        totalRunning: 1,
        totalCompleted: 15,
        totalFailed: 3
      };

      const mockExecutions: ExecutionRecord[] = [
        {
          id: 'exec_1',
          workflowId: 'workflow_1',
          workflowName: 'Data Processing Workflow',
          status: 'running',
          startTime: new Date(Date.now() - 120000).toISOString(),
          progress: 65,
          userId: 'user_1'
        },
        {
          id: 'exec_2',
          workflowId: 'workflow_2',
          workflowName: 'Email Campaign Workflow',
          status: 'queued',
          startTime: new Date(Date.now() - 60000).toISOString(),
          progress: 0,
          userId: 'user_2'
        },
        {
          id: 'exec_3',
          workflowId: 'workflow_1',
          workflowName: 'Data Processing Workflow',
          status: 'completed',
          startTime: new Date(Date.now() - 300000).toISOString(),
          endTime: new Date(Date.now() - 180000).toISOString(),
          progress: 100,
          userId: 'user_1'
        },
        {
          id: 'exec_4',
          workflowId: 'workflow_3',
          workflowName: 'Report Generation',
          status: 'failed',
          startTime: new Date(Date.now() - 400000).toISOString(),
          endTime: new Date(Date.now() - 350000).toISOString(),
          progress: 45,
          error: 'Database connection timeout',
          userId: 'user_3'
        }
      ];

      setQueueStats(mockStats);
      setExecutions(mockExecutions);
    } catch (error) {
      console.error('Error loading queue data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === 'completed' ? 'default' : 
                   status === 'failed' ? 'destructive' : 
                   status === 'running' ? 'secondary' : 'outline';
    
    return (
      <Badge variant={variant} className="flex items-center gap-1">
        {getStatusIcon(status)}
        {status}
      </Badge>
    );
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = end.getTime() - start.getTime();
    
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
    return `${(duration / 60000).toFixed(1)}m`;
  };

  const filteredExecutions = executions.filter(execution => {
    if (statusFilter !== 'all' && execution.status !== statusFilter) return false;
    if (selectedWorkflow && selectedWorkflow !== 'all' && execution.workflowId !== selectedWorkflow) return false;
    return true;
  });

  const uniqueWorkflows = Array.from(new Set(executions.map(e => e.workflowId)));

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Queue Management</h1>
          <p className="text-muted-foreground">Monitor and manage workflow execution queue</p>
        </div>
        <Button onClick={loadQueueData} disabled={loading} className="flex items-center gap-2">
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Queue Statistics */}
      {queueStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                <div>
                  <div className="text-2xl font-bold text-yellow-600">{queueStats.totalQueued}</div>
                  <div className="text-sm text-muted-foreground">Queued</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Play className="h-5 w-5 text-blue-500" />
                <div>
                  <div className="text-2xl font-bold text-blue-600">{queueStats.totalRunning}</div>
                  <div className="text-sm text-muted-foreground">Running</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <div className="text-2xl font-bold text-green-600">{queueStats.totalCompleted}</div>
                  <div className="text-sm text-muted-foreground">Completed</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <XCircle className="h-5 w-5 text-red-500" />
                <div>
                  <div className="text-2xl font-bold text-red-600">{queueStats.totalFailed}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="workflow-filter">Workflow</Label>
              <Select value={selectedWorkflow} onValueChange={setSelectedWorkflow}>
                <SelectTrigger>
                  <SelectValue placeholder="All workflows" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All workflows</SelectItem>
                  {uniqueWorkflows.map(workflowId => (
                    <SelectItem key={workflowId} value={workflowId}>
                      {workflowId}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <Label htmlFor="status-filter">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="queued">Queued</SelectItem>
                  <SelectItem value="running">Running</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Execution History */}
      <Card>
        <CardHeader>
          <CardTitle>Execution Queue ({filteredExecutions.length})</CardTitle>
          <CardDescription>All workflow executions across the system</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading queue data...</span>
            </div>
          ) : filteredExecutions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No executions found matching the current filters.
            </div>
          ) : (
            <ScrollArea className="h-96">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Workflow</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Started</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredExecutions.map((execution) => (
                    <TableRow key={execution.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{execution.workflowName || execution.workflowId}</div>
                          <div className="text-sm text-muted-foreground">{execution.workflowId}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(execution.status)}
                        {execution.error && (
                          <div className="text-xs text-red-600 mt-1 max-w-xs truncate" title={execution.error}>
                            {execution.error}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(execution.startTime).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {formatDuration(execution.startTime, execution.endTime)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${execution.progress}%` }}
                            />
                          </div>
                          <span className="text-sm">{execution.progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{execution.userId}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Navigate to execution results
                              window.open(`/workflow-canvas/${execution.workflowId}`, '_blank');
                            }}
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          {execution.status === 'queued' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                // Cancel execution
                                console.log('Cancel execution:', execution.id);
                              }}
                            >
                              <Square className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
