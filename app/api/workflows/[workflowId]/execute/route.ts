import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { WorkflowExecutionEngine } from "@/lib/workflow/execution-engine";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    // Await params to fix Next.js 15 requirement
    const { workflowId } = await params;

    // Get workflow (no authentication required)
    const workflow = await prisma.workflow.findFirst({
      where: {
        id: workflowId
      }
    });

    if (!workflow) {
      return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    const {
      executionOptions = {},
      variables = {},
      triggerNodeId = null
    } = body;

    // Create execution record (use anonymous user for public access)
    const execution = await prisma.workflowExecution.create({
      data: {
        id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        workflowId: workflowId,
        userId: workflow.userId || 'anonymous',
        status: 'running',
        startTime: new Date(),
        variables: JSON.stringify(variables),
        options: JSON.stringify(executionOptions),
        triggerNodeId,
        logs: JSON.stringify([{
          timestamp: new Date(),
          level: 'info',
          message: 'Execution started'
        }])
      }
    });

    // Use the real execution engine
    try {
      // Parse workflow nodes and edges
      const nodes = JSON.parse(workflow.nodes || '[]');
      const edges = JSON.parse(workflow.edges || '[]');

      console.log(`[Quick Execute] Starting execution for workflow ${workflowId} with ${nodes.length} nodes`);

      // Create execution context
      const executionContext = {
        workflowId: workflowId,
        userId: workflow.userId || 'anonymous',
        variables,
        secrets: {}, // TODO: Implement secrets management
        settings: {}, // TODO: Implement settings management
        log: (message: string) => console.log(`[Workflow ${workflowId}]: ${message}`)
      };

      // Get execution engine instance
      const executionEngine = WorkflowExecutionEngine.getInstance();

      // Execute the workflow using the real engine (continueOnError: true by default)
      const workflowStatus = await executionEngine.executeWorkflow(
        nodes,
        edges,
        executionContext,
        {
          mode: 'sequence',
          timeout: 30000,
          retryAttempts: 3,
          continueOnError: true,
          debugMode: false,
          maxConcurrentNodes: 3,
          ...executionOptions
        }
      );

      console.log(`[Quick Execute] Workflow execution completed with status: ${workflowStatus.status}`);

      // Extract results and logs from the execution status
      const results = workflowStatus.results || {};
      const logs = workflowStatus.logs || [];

      // Determine final status
      const finalStatus = workflowStatus.status;

      // Update execution record
      await prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          status: finalStatus,
          endTime: workflowStatus.endTime || new Date(),
          results: JSON.stringify(results),
          logs: JSON.stringify(logs.map(log => ({
            timestamp: new Date(),
            level: 'info',
            message: log
          }))),
          error: finalStatus === 'failed' ? 'Workflow execution failed' : null
        }
      });

      console.log(`[Quick Execute] Database updated with execution results`);

      return NextResponse.json({
        executionId: execution.id,
        status: finalStatus,
        results,
        logs,
        startTime: execution.startTime,
        endTime: workflowStatus.endTime || new Date(),
        progress: workflowStatus.progress,
        completedNodes: workflowStatus.completedNodes,
        failedNodes: workflowStatus.failedNodes
      });

    } catch (executionError) {
      console.error('[Quick Execute] Execution error:', executionError);

      // Update execution record with error
      await prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          status: 'failed',
          endTime: new Date(),
          error: executionError instanceof Error ? executionError.message : 'Execution failed',
          logs: JSON.stringify([
            ...JSON.parse(execution.logs || '[]'),
            {
              timestamp: new Date(),
              level: 'error',
              message: executionError instanceof Error ? executionError.message : 'Execution failed'
            }
          ])
        }
      });

      throw executionError;
    }

  } catch (error) {
    console.error('Workflow execution error:', error);
    return NextResponse.json({
      error: 'Execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    // Await params to fix Next.js 15 requirement
    const { workflowId } = await params;

    // Get execution history for this workflow (no authentication required)
    const executions = await prisma.workflowExecution.findMany({
      where: {
        workflowId: workflowId
      },
      orderBy: { startTime: 'desc' },
      take: 20 // Limit to last 20 executions
    });

    const formattedExecutions = executions.map(execution => ({
      id: execution.id,
      status: execution.status,
      startTime: execution.startTime,
      endTime: execution.endTime,
      duration: execution.endTime && execution.startTime
        ? execution.endTime.getTime() - execution.startTime.getTime()
        : null,
      variables: JSON.parse(execution.variables || '{}'),
      options: JSON.parse(execution.options || '{}'),
      results: execution.results ? JSON.parse(execution.results) : null,
      logs: execution.logs ? JSON.parse(execution.logs) : [],
      error: execution.error,
      triggerNodeId: execution.triggerNodeId
    }));

    return NextResponse.json({
      executions: formattedExecutions
    });

  } catch (error) {
    console.error('Execution history fetch error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
